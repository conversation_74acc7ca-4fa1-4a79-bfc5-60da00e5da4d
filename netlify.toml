# This netlify.toml is for the CLIENT application (eatzone1.netlify.app)
# For admin panel deployment, use manual deployment of admin/dist folder

[build]
  base = "client"
  command = "npm ci --include=dev && npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--include=dev"
  VITE_API_BASE_URL = "https://eatzone.onrender.com"
  VITE_APP_ENV = "production"

# EatZone Client Application - Updated: 2025-07-23

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
