/* Modern Admin Dashboard Styles */
:root {
  /* Eatzone Brand Colors */
  --primary-red: #ff4757;
  --primary-red-hover: #ff3742;
  --primary-red-light: #fff0f0;
  --accent-black: #2f3542;
  --light-gray: #f8f9fa;
  --medium-gray: #e9ecef;
  --dark-gray: #6c757d;
  --white: #ffffff;
  --success: #28a745;
  --warning: #ffc107;
  --danger: #dc3545;
  --info: #17a2b8;
  --purple: #6f42c1;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Transitions */
  --transition: all 0.3s ease;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  min-height: 100vh;
  background-color: var(--light-gray);
  color: var(--accent-black);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition);
}

/* App Layout Styles */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-content {
  display: flex;
  flex: 1;
  min-height: calc(100vh - 80px); /* Adjust based on navbar height */
}

.main-content {
  flex: 1;
  padding: 2rem;
  background-color: var(--light-gray);
  overflow-y: auto;
  transition: all 0.3s ease;
  margin-left: 0;
}

/* Desktop: Adjust main content based on sidebar state */
@media (min-width: 1025px) {
  .main-content.sidebar-open {
    margin-left: 280px; /* Sidebar width */
  }

  .main-content.sidebar-closed {
    margin-left: 0;
  }
}

/* Mobile: Always no margin for main content */
@media (max-width: 1024px) {
  .main-content {
    margin-left: 0 !important;
  }

  .main-content.sidebar-open,
  .main-content.sidebar-closed {
    margin-left: 0 !important;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
}

/* Layout */
.app-content {
  display: flex;
  min-height: calc(100vh - 80px);
  position: relative;
}

.main-content {
  flex: 1;
  min-width: 0;
  transition: var(--transition);
}

/* Mobile menu overlay */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 50;
  animation: fadeIn 0.3s ease;
}

.mobile-overlay.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Utility Classes */
.flex-col {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.flex-row {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cursor {
  cursor: pointer;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* Card Components */
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 1.5rem;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  border-bottom: 1px solid var(--medium-gray);
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--accent-black);
  margin: 0;
}

.card-subtitle {
  font-size: 0.875rem;
  color: var(--dark-gray);
  margin-top: 0.25rem;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-red);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-red-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--medium-gray);
  color: var(--accent-black);
}

.btn-secondary:hover {
  background-color: var(--dark-gray);
  color: var(--white);
}

.btn-success {
  background-color: var(--success);
  color: var(--white);
}

.btn-danger {
  background-color: var(--danger);
  color: var(--white);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--accent-black);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--medium-gray);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: var(--transition);
  background-color: var(--white);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--medium-gray);
}

.table th {
  background-color: var(--light-gray);
  font-weight: 600;
  color: var(--accent-black);
}

.table tr:hover {
  background-color: var(--light-gray);
}

/* Responsive Table */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive .table {
  min-width: 600px;
}

/* Status Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.badge-warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.badge-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger);
}

.badge-info {
  background-color: rgba(23, 162, 184, 0.1);
  color: var(--info);
}

/* Grid System */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive */
@media (max-width: 1200px) {
  .grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-content {
    margin-left: 240px;
  }
}

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }

  .app-content {
    flex-direction: row;
  }

  .main-content {
    margin-left: 60px;
    padding: 0;
  }

  /* Adjust padding for mobile */
  .card {
    padding: 1rem;
  }

  .btn {
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
  }

  .form-input,
  .form-select,
  .form-textarea {
    padding: 0.625rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .main-content {
    margin-left: 0;
    padding: 0.5rem;
  }

  .card {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .form-input,
  .form-select,
  .form-textarea {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
}

/* Loading Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--medium-gray);
  border-top: 2px solid var(--primary-red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
