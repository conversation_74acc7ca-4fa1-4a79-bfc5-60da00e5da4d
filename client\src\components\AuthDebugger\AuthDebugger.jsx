import { useContext, useEffect, useState } from 'react';
import { StoreContext } from '../../context/StoreContext';
import './AuthDebugger.css';

const AuthDebugger = () => {
  const { user, token, isUserLoading } = useContext(StoreContext);
  const [debugInfo, setDebugInfo] = useState({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateDebugInfo = () => {
      const storedToken = localStorage.getItem('token');
      const storedUser = localStorage.getItem('user');
      
      setDebugInfo({
        contextToken: token ? `${token.substring(0, 20)}...` : 'None',
        contextUser: user ? user.name : 'None',
        storedToken: storedToken ? `${storedToken.substring(0, 20)}...` : 'None',
        storedUser: storedUser ? JSON.parse(storedUser).name : 'None',
        isUserLoading,
        timestamp: new Date().toLocaleTimeString()
      });
    };

    updateDebugInfo();
    
    // Update every 2 seconds
    const interval = setInterval(updateDebugInfo, 2000);
    return () => clearInterval(interval);
  }, [token, user, isUserLoading]);

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className={`auth-debugger ${isVisible ? 'visible' : 'hidden'}`}>
      <button 
        className="debug-toggle"
        onClick={() => setIsVisible(!isVisible)}
      >
        🔍 Auth Debug
      </button>
      
      {isVisible && (
        <div className="debug-panel">
          <h4>Authentication Debug Info</h4>
          <div className="debug-item">
            <strong>Context Token:</strong> {debugInfo.contextToken}
          </div>
          <div className="debug-item">
            <strong>Context User:</strong> {debugInfo.contextUser}
          </div>
          <div className="debug-item">
            <strong>Stored Token:</strong> {debugInfo.storedToken}
          </div>
          <div className="debug-item">
            <strong>Stored User:</strong> {debugInfo.storedUser}
          </div>
          <div className="debug-item">
            <strong>Loading:</strong> {debugInfo.isUserLoading ? 'Yes' : 'No'}
          </div>
          <div className="debug-item">
            <strong>Last Update:</strong> {debugInfo.timestamp}
          </div>
          
          <div className="debug-actions">
            <button onClick={() => {
              console.log('🔍 Current localStorage token:', localStorage.getItem('token'));
              console.log('🔍 Current localStorage user:', localStorage.getItem('user'));
              console.log('🔍 Current context token:', token);
              console.log('🔍 Current context user:', user);
            }}>
              Log to Console
            </button>
            
            <button onClick={() => {
              localStorage.removeItem('token');
              localStorage.removeItem('user');
              window.location.reload();
            }}>
              Clear & Reload
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthDebugger;
