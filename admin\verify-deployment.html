<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .timestamp { font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 EatZone Admin Panel Deployment Verification</h1>
        
        <div class="info">
            <strong>Expected Version:</strong> v3.0 - NO LOGIN<br>
            <strong>Expected Title:</strong> "EatZone Admin Panel - NO LOGIN v3.0"<br>
            <strong>Expected Behavior:</strong> Direct access to admin dashboard (no login page)
        </div>

        <button onclick="checkDeployment()">🔍 Check Current Deployment</button>
        <button onclick="clearCache()">🗑️ Clear Browser Cache</button>
        <button onclick="openAdmin()">🔗 Open Admin Panel</button>

        <div id="results"></div>

        <div class="timestamp">
            Last check: <span id="lastCheck">Never</span>
        </div>
    </div>

    <script>
        function checkDeployment() {
            const results = document.getElementById('results');
            const lastCheck = document.getElementById('lastCheck');
            
            results.innerHTML = '<div class="info">Checking deployment...</div>';
            
            fetch('https://eatzone.netlify.app/')
                .then(response => response.text())
                .then(html => {
                    const isNewVersion = html.includes('NO LOGIN v3.0');
                    const hasLoginForm = html.includes('Welcome Back') || html.includes('Sign in to your admin account');
                    
                    let status = '';
                    if (isNewVersion && !hasLoginForm) {
                        status = '<div class="success">✅ SUCCESS: New version deployed! No login page detected.</div>';
                    } else if (hasLoginForm) {
                        status = '<div class="error">❌ OLD VERSION: Login page still present. Deployment may be in progress.</div>';
                    } else {
                        status = '<div class="error">❌ UNKNOWN: Could not determine version. Check manually.</div>';
                    }
                    
                    results.innerHTML = status + 
                        '<details style="margin-top: 10px;"><summary>Technical Details</summary>' +
                        '<pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px;">' +
                        'Version 3.0 detected: ' + isNewVersion + '\n' +
                        'Login form detected: ' + hasLoginForm + '\n' +
                        'Title contains "NO LOGIN v3.0": ' + html.includes('NO LOGIN v3.0') +
                        '</pre></details>';
                    
                    lastCheck.textContent = new Date().toLocaleString();
                })
                .catch(error => {
                    results.innerHTML = '<div class="error">❌ ERROR: Could not check deployment. ' + error.message + '</div>';
                    lastCheck.textContent = new Date().toLocaleString();
                });
        }

        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // Force reload without cache
            window.location.reload(true);
        }

        function openAdmin() {
            window.open('https://eatzone.netlify.app/', '_blank');
        }

        // Auto-check on page load
        window.onload = () => {
            setTimeout(checkDeployment, 1000);
        };
    </script>
</body>
</html>
