{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "lucide-react": "^0.510.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.1", "vite": "^5.4.10", "@vitejs/plugin-react": "^4.2.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "terser": "^5.43.1"}}