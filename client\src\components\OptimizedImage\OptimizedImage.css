.optimized-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 8px;
  display: block;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden;
}

.optimized-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: opacity 0.2s ease-in-out; /* Faster transition for better perceived performance */
  border-radius: inherit;
  display: block;
  margin: 0;
  padding: 0;
  /* Performance optimizations */
  will-change: opacity;
  transform: translateZ(0);
}

.optimized-image.loading {
  opacity: 0;
}

.optimized-image.loaded {
  opacity: 1;
}

/* Priority loading for restaurant images */
.optimized-image.priority-load {
  /* Fastest possible loading optimizations */
  will-change: opacity, transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  /* Reduce transition time for instant appearance */
  transition: opacity 0.1s ease-in-out;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-radius: inherit;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.placeholder-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.7;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f8f8 0%, #eeeeee 100%);
  color: #888;
  border-radius: inherit;
  border: 2px dashed #ddd;
  z-index: 100;
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 1;
}

.error-text {
  font-size: 0.9rem;
  text-align: center;
  opacity: 1;
  font-weight: 700;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .placeholder-icon,
  .error-icon {
    font-size: 1.5rem;
  }
  
  .error-text {
    font-size: 0.7rem;
  }
  
  .spinner {
    width: 20px;
    height: 20px;
  }
}

/* Animation for smooth loading */
.optimized-image-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
